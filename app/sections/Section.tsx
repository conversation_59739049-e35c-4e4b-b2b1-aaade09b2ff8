import React from 'react';
import type {
  InspectorGroup,
  HydrogenComponentProps,
  HydrogenComponentSchema,
} from '@weaverse/hydrogen';
import type {VariantProps} from 'class-variance-authority';
import {cva} from 'class-variance-authority';
import {cn} from '~/lib/utils';
import {forwardRef} from 'react';

let variants = cva('relative', {
  variants: {
    horizontal: {
      full: 'w-full',
      custom: 'w-[--section-width]',
    },
    contentPosition: {
      'center center': 'justify-center items-center',
      'top center': 'justify-center items-start',
      'bottom center': 'justify-center items-end',
      'top left': 'justify-start items-start',
      'top right': 'justify-end items-start',
      'bottom left': 'justify-start items-end',
      'bottom right': 'justify-end items-end',
    },
    horizontalPadding: {
      none: '',
      small: 'px-4 md:px-6 lg:px-8',
      medium: 'px-8 md:px-12 lg:px-16',
      large: 'px-12 md:px-24 lg:px-32',
    },
    verticalPadding: {
      none: '',
      small: 'py-4 md:py-6 lg:py-8',
      medium: 'py-8 md:py-12 lg:py-16',
      large: 'py-12 md:py-24 lg:py-32',
    },
  },
});

export interface SectionProps<T = any>
  extends Partial<Omit<HydrogenComponentProps<T>, 'children'>>,
    VariantProps<typeof variants> {
  as?: React.ElementType;
  width?: number;
  maxWidth?: number;
  backgroundColor?: string;
  children?: React.ReactNode;
}

const Section = forwardRef<HTMLDivElement, SectionProps>((props, ref) => {
  let {
    as: Component = 'section',
    horizontal,
    horizontalPadding,
    verticalPadding,
    contentPosition,
    width,
    maxWidth,
    backgroundColor,
    children,
    ...rest
  } = props;

  let style = {} as React.CSSProperties;
  if (backgroundColor) {
    style['--section-bg-color'] = backgroundColor;
  }
  let contentStyle = {} as React.CSSProperties;
  // if (width) {
  //   contentStyle['--section-width'] = `${width}px`;
  // }
  if (maxWidth) {
    contentStyle['maxWidth'] = `${maxWidth}px`;
  }

  let hasBackground = backgroundColor;

  return (
    <Component
      ref={ref}
      {...rest}
      style={style}
      className={cn(
        'flex',
        variants({contentPosition}),
        hasBackground && `bg-[--section-bg-color]`,
      )}
    >
      <div
        style={contentStyle}
        className={cn(
          variants({horizontal, horizontalPadding, verticalPadding}),
        )}
      >
        {children}
      </div>
    </Component>
  );
});

export default Section;

export const layoutInputs: InspectorGroup['inputs'] = [
  {
    type: 'select',
    name: 'horizontal',
    label: 'Horizontal',
    configs: {
      options: [
        {value: 'full', label: 'Full'},
        {value: 'custom', label: 'Custom'},
      ],
    },
    defaultValue: 'full',
  },
  // {
  //   type: 'range',
  //   name: 'width',
  //   label: 'Width',
  //   configs: {
  //     min: 0,
  //     max: 4000,
  //     step: 1,
  //     unit: 'px',
  //   },
  //   defaultValue: undefined,
  //   condition: 'horizontal.eq.custom',
  // },
  {
    type: 'range',
    name: 'maxWidth',
    label: 'Max Width',
    configs: {
      min: 0,
      max: 4000,
      step: 1,
      unit: 'px',
    },
    defaultValue: 1200,
    condition: 'horizontal.eq.custom',
  },
  {
    type: 'position',
    name: 'contentPosition',
    label: 'Content Position',
    defaultValue: 'center center',
  },
  {
    type: 'select',
    name: 'horizontalPadding',
    label: 'Horizontal padding',
    configs: {
      options: [
        {value: 'none', label: 'None'},
        {value: 'small', label: 'Small'},
        {value: 'medium', label: 'Medium'},
        {value: 'large', label: 'Large'},
      ],
    },
    defaultValue: 'none',
  },
  {
    type: 'select',
    name: 'verticalPadding',
    label: 'Vertical padding',
    configs: {
      options: [
        {value: 'none', label: 'None'},
        {value: 'small', label: 'Small'},
        {value: 'medium', label: 'Medium'},
        {value: 'large', label: 'Large'},
      ],
    },
    defaultValue: 'none',
  },
];

export const backgroundInputs: InspectorGroup['inputs'] = [
  {
    type: 'color',
    name: 'backgroundColor',
    label: 'Background Color',
  },
];

export const schema: HydrogenComponentSchema = {
  type: 'section',
  title: 'Section',
  childTypes: ['text-block'],
  inspector: [
    {
      group: 'Layout',
      inputs: layoutInputs,
    },
    {
      group: 'Background',
      inputs: backgroundInputs,
    },
  ],
};
