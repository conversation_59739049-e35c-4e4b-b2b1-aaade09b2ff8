import React from 'react';
import type {
  ComponentLoaderArgs,
  HydrogenComponentSchema,
} from '@weaverse/hydrogen';
import {forwardRef} from 'react';
import {HEADER_QUERY} from '~/lib/fragments';

const TextBlock = forwardRef<
  HTMLDivElement,
  {content?: string; loaderData?: {}}
>((props, ref) => {
  let {loaderData, content} = props;
  console.log(loaderData);
  return (
    <div ref={ref}>
      <div dangerouslySetInnerHTML={{__html: content}}></div>
    </div>
  );
});

export default TextBlock;

export let loader = async ({weaverse}: ComponentLoaderArgs) => {
  //   return weaverse.storefront.query(HEADER_QUERY, {
  //     variables: {
  //       headerMenuHandle: 'main-menu',
  //     },
  //   });
  //   return weaverse.customerAccount.isLoggedIn();
};

export const schema: HydrogenComponentSchema = {
  type: 'text-block',
  title: 'Text Block',
  inspector: [
    {
      group: 'Settings',
      inputs: [
        {
          type: 'richtext',
          name: 'content',
          label: 'Text content',
        },
      ],
    },
  ],
};
