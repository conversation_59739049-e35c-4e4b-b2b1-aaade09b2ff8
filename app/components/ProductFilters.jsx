import {useRef, createRef, useState, useEffect} from 'react';
import {Form, useSubmit, useSearchParams} from '@remix-run/react';
import {Check, PlusCircle, X} from 'lucide-react';
import {Popover, PopoverContent, PopoverTrigger} from '~/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '~/components/ui/command';
import {Separator} from '~/components/ui/separator';
import {Button} from '~/components/ui/button';
import {Badge} from '~/components/ui/badge';
import MaterialTailwind from '@material-tailwind/react';
const {select} = MaterialTailwind;

export function ProductFilters({activeFilters, filters}) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedFilters, setSelectedFilters] = useState(activeFilters);
  const submit = useSubmit();
  const formRef = useRef();
  const inputRefs = useRef({});
  const validFilters = filters.filter(
    (filter) => filter.type === 'LIST' && filter.label !== 'Tags',
  );
  //console.log(validFilters);

  useEffect(() => {
    const isEmpty = selectedFilters.length === 0;
    // submit(isEmpty ? {} : {filters: JSON.stringify(selectedFilters)}, {
    //   replace: true,
    // });
    const newParams = new URLSearchParams();
    if (searchParams.has('q')) {
      newParams.set('q', searchParams.get('q'));
    }
    if (!isEmpty) {
      newParams.set('filters', JSON.stringify(selectedFilters));
    }
    setSearchParams(
      newParams,
      // (prev) => {
      //   if (isEmpty) {
      //     prev.delete('filters');
      //   } else {
      //     prev.set('filters', JSON.stringify(selectedFilters));
      //   }
      //   return prev;
      // },
      {
        replace: true,
      },
    );
  }, [selectedFilters]);

  if (validFilters && validFilters.length > 0) {
    return (
      <div className="flex flex-col">
        {/* <p className="text-sm font-bold">Filters:</p> */}
        <Form className="flex flex-wrap items-center gap-2" ref={formRef}>
          {validFilters.map((filter) => {
            const thisSelected = selectedFilters.filter((selectedFilter) =>
              filter.id.includes(selectedFilter[0]),
            );
            return (
              <div key={filter.id}>
                <FilterButton
                  formRef={formRef}
                  inputRefs={inputRefs}
                  filter={filter}
                  thisSelected={thisSelected}
                  selectedFilters={selectedFilters}
                  setSelectedFilters={setSelectedFilters}
                />
              </div>
            );
          })}
          {selectedFilters.length > 0 && (
            <Button
              variant="destructive"
              onClick={() => setSelectedFilters([])}
              className="h-8 px-2 lg:px-3"
            >
              Reset filters <X />
            </Button>
          )}
        </Form>
      </div>
    );
  }
  return <></>;
}

function FilterButton({
  formRef,
  inputRefs,
  filter,
  thisSelected,
  selectedFilters,
  setSelectedFilters,
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 border-dashed">
          <PlusCircle />
          {filter.label}
          {thisSelected.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge
                variant="secondary"
                className="rounded-sm px-1 font-normal lg:hidden"
              >
                {thisSelected.length} selected
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {thisSelected.length > 2 ? (
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal"
                  >
                    {thisSelected.length} selected
                  </Badge>
                ) : (
                  thisSelected.map((filter) => (
                    <Badge
                      variant="secondary"
                      key={filter[1]}
                      className="rounded-sm px-1 font-normal"
                    >
                      {filter[1]}
                    </Badge>
                  ))
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        portalContainer={formRef.current}
        className="p-0"
        align="start"
      >
        <Command>
          <CommandInput placeholder={filter.label} className="p-1" />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {filter.values.map((value) => {
                const isSelected = selectedFilters.some((filter) => {
                  return filter[1] === value.label;
                });
                const inputRef = inputRefs.current[value.id] || createRef();
                inputRefs.current[value.id] = inputRef;

                return (
                  <CommandItem key={value.id}>
                    <input
                      ref={inputRef}
                      type="checkbox"
                      id={value.id}
                      name={value.label}
                      value={value.input}
                      checked={isSelected}
                      onChange={() => {
                        if (isSelected) {
                          // Remove filter from list
                          setSelectedFilters((prevFilters) =>
                            prevFilters.reduce((acc, filter) => {
                              if (filter[1] !== value.label) {
                                acc.push(filter);
                              }
                              return acc;
                            }, []),
                          );
                        } else {
                          // Add filter to list
                          const inputData = JSON.parse(value.input);
                          const tag = inputData?.tag;
                          if (tag) {
                            setSelectedFilters((prevFilters) => [
                              ...prevFilters,
                              ['tag', tag],
                            ]);
                          }
                          //console.log(inputData);
                          const metafield = inputData?.productMetafield;
                          if (metafield) {
                            setSelectedFilters((prevFilters) => [
                              ...prevFilters,
                              [
                                `${metafield.namespace}.${metafield.key}`,
                                metafield.value,
                              ],
                            ]);
                          }
                        }
                      }}
                    />
                    <label htmlFor={value.id} className="p-2">
                      {value.label} ({value.count})
                    </label>
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {thisSelected.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setSelectedFilters((prevFilters) =>
                        prevFilters.filter(
                          (selectedFilter) =>
                            !thisSelected.some(
                              (selected) => selected[1] === selectedFilter[1],
                            ),
                        ),
                      );
                    }}
                    className="justify-center text-center"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
