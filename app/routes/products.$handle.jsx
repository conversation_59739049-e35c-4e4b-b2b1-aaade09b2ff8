import {Suspense, useState} from 'react';
import {defer, redirect} from '@shopify/remix-oxygen';
import {
  Await,
  NavLink,
  Link,
  data,
  useSearchParams,
  useLoaderData,
  useRouteLoaderData,
  useNavigation,
  Outlet,
} from '@remix-run/react';
import {
  getSelectedProductOptions,
  Analytics,
  useOptimisticVariant,
} from '@shopify/hydrogen';
import {useAside} from '~/components/Aside';
import {Loading} from '~/components/Loading';
import {getVariantUrl} from '~/lib/variants';
import {QuantitySelector} from '~/components/QuantitySelector';
import {AddToCartButton} from '~/components/AddToCartButton';
import {ProductPrice} from '~/components/ProductPrice';
import {ProductImage} from '~/components/ProductImage';
import {ProductVariantSelector} from '~/components/ProductVariantSelector';
import {LoginToViewPriceButton} from '~/components/LoginToViewPriceButton';
import {Breadcrumbs} from '~/components/Breadcrumbs';
import {RelatedProducts} from '~/components/RelatedProducts';
import {HEADER_QUERY} from '~/lib/fragments';
import {findBreadcrumbsByTitles, getMenuUrl} from '~/lib/utils';
import {WeaverseContent} from '~/weaverse';

/**
 * @type {MetaFunction<typeof loader>}
 */
export const meta = ({data}) => {
  return [
    {title: `${data?.product.title ?? ''} | Glove Box Australia Pty Ltd`},
  ];
};

/**
 * @param {LoaderFunctionArgs} args
 */
export async function loader(args) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return defer({...deferredData, ...criticalData});
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 * @param {LoaderFunctionArgs}
 */
async function loadCriticalData({context, params, request}) {
  const {handle} = params;
  const {storefront} = context;

  if (!handle) {
    throw new Error('Expected product handle to be defined');
  }

  const selectedOptions = getSelectedProductOptions(request);
  const [{product}, {menu}] = await Promise.all([
    storefront.query(PRODUCT_QUERY, {
      variables: {
        handle,
        selectedOptions,
      },
    }),
    storefront.query(HEADER_QUERY, {
      variables: {
        headerMenuHandle: 'main-menu',
      },
    }),
    // Add other queries here, so that they are loaded in parallel
  ]);

  if (!product?.id) {
    throw new Response(null, {status: 404});
  }

  if (!product.selectedVariant) {
    throw redirectToFirstVariant({product, request});
  }

  const {nodes: collections} = product.collections;
  const titles = collections.map((collection) => collection.title);
  const breadcrumbs = findBreadcrumbsByTitles(menu.items, titles);
  breadcrumbs.push({
    title: product.title,
  });

  return {
    product,
    breadcrumbs,
    menu,
    weaverseData: await context.weaverse.loadPage({
      type: 'PRODUCT',
      handle: handle,
    }),
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 * @param {LoaderFunctionArgs}
 */
function loadDeferredData({context, params}) {
  // In order to show which variants are available in the UI, we need to query
  // all of them. But there might be a *lot*, so instead separate the variants
  // into it's own separate query that is deferred. So there's a brief moment
  // where variant options might show as available when they're not, but after
  // this deffered query resolves, the UI will update.
  const variants = context.storefront
    .query(VARIANTS_QUERY, {
      variables: {handle: params.handle},
    })
    .catch((error) => {
      // Log query errors, but don't throw them so the page can still render
      console.error(error);
      return null;
    });

  return {
    variants,
  };
}

/**
 * @param {{
 *   product: ProductFragment;
 *   request: Request;
 * }}
 */
function redirectToFirstVariant({product, request}) {
  const url = new URL(request.url);
  const firstVariant = product.variants.nodes[0];

  return redirect(
    getVariantUrl({
      pathname: url.pathname,
      handle: product.handle,
      selectedOptions: firstVariant.selectedOptions,
      searchParams: new URLSearchParams(url.search),
    }),
    {
      status: 302,
    },
  );
}

export default function Product() {
  const {state} = useNavigation();
  const {customer} = useRouteLoaderData('root');
  /** @type {LoaderReturnData} */
  const {product, breadcrumbs, menu} = useLoaderData();
  const {open} = useAside();
  const {
    handle,
    title,
    descriptionHtml,
    selectedVariant,
    moq,
    shortDescription,
    technicalData,
  } = product;
  const shortDescriptionHtml = shortDescription?.value || '';
  const minQuantity = moq?.value ? parseInt(moq.value, 10) : 1;

  const parentCollection =
    breadcrumbs.length > 1 && breadcrumbs[breadcrumbs.length - 2];

  const [quantity, setQuantity] = useState(minQuantity);

  return (
    <div className="p-4 flex flex-col items-center">
      <Breadcrumbs breadcrumbs={breadcrumbs} />
      <div className="page-container p-4">
        <div className="product">
          <div className="flex flex-col items-center">
            <ProductImage product={product} />
          </div>
          <div className="product-main p-4">
            <Link
              to={getMenuUrl(parentCollection)}
              className="text-[#215387] uppercase"
            >
              {parentCollection?.title}
            </Link>
            <h1 className="text-2xl font-medium py-1 text-black">{title}</h1>
            <p className="text-sm italic">
              SKU: {state === 'idle' ? selectedVariant?.sku : <Loading />}
            </p>
            <br />
            <div
              dangerouslySetInnerHTML={{__html: shortDescriptionHtml}}
              className="product-description mt-1"
            />
            <br />
            <ProductVariantSelector
              product={product}
              selectedVariant={selectedVariant}
              variants={[]}
            />
            <Suspense fallback={<Loading />}>
              <Await resolve={customer}>
                {({status: {isLoggedIn, isApproved}}) =>
                  isLoggedIn ? (
                    // Logged in
                    isApproved ? (
                      // Approved
                      <>
                        <div className="flex items-center gap-4">
                          <ProductPrice
                            price={selectedVariant?.price}
                            compareAtPrice={selectedVariant?.compareAtPrice}
                            state={state}
                            className="text-lg font-medium"
                          />
                          {selectedVariant?.price?.amount > 0 && (
                            <>
                              <QuantitySelector
                                quantity={quantity}
                                setQuantity={setQuantity}
                                min={minQuantity}
                              />
                              <AddToCartButton
                                selectedVariant={selectedVariant}
                                quantity={quantity}
                                minQuantity={minQuantity}
                                disabled={state !== 'idle'}
                                onClick={() => {
                                  open('cart');
                                }}
                              >
                                {state === 'idle' ? (
                                  'Add to cart'
                                ) : (
                                  <Loading className="text-white" />
                                )}
                              </AddToCartButton>
                            </>
                          )}
                        </div>
                      </>
                    ) : (
                      // Not approved
                      <p className="text-gba-blue">
                        Price available after account approval
                      </p>
                    )
                  ) : (
                    // Not logged in
                    <LoginToViewPriceButton />
                  )
                }
              </Await>
            </Suspense>
          </div>
        </div>
        <br />
        <div className="flex flex-col items-center justify-center">
          <ProductTabs {...product} />
          <Outlet context={{product}} />
        </div>
        <br />
        <br />
        <RelatedProducts product={product} />
      </div>
      <Analytics.ProductView
        data={{
          products: [
            {
              id: product.id,
              title: product.title,
              price: selectedVariant?.price.amount || '0',
              vendor: product.vendor,
              variantId: selectedVariant?.id || '',
              variantTitle: selectedVariant?.title || '',
              quantity: 1,
            },
          ],
        }}
      />
    </div>
  );
}

function ProductTabs({
  handle,
  description,
  technicalData,
  downloads,
  relatedProducts,
}) {
  const [searchParams] = useSearchParams();
  return (
    <nav
      role="navigation"
      className="text-xl border-b border-gray-200 dark:border-gray-700"
    >
      {description && (
        <Tab
          pathname={`/products/${handle}/description`}
          search={searchParams.toString()}
        >
          Description
        </Tab>
      )}
      {technicalData && (
        <Tab
          pathname={`/products/${handle}/technical-data`}
          search={searchParams.toString()}
        >
          Technical Data
        </Tab>
      )}
      {downloads && (
        <Tab
          pathname={`/products/${handle}/downloads`}
          search={searchParams.toString()}
        >
          Downloads
        </Tab>
      )}
    </nav>
  );
}

function Tab({pathname, search, children}) {
  function isActiveStyle({isActive, isPending}) {
    return {
      fontWeight: isActive ? 'bold' : undefined,
      borderColor: isActive ? 'black' : 'transparent',
      color: isPending ? 'grey' : 'black',
    };
  }
  return (
    <NavLink
      to={{
        pathname: pathname,
        search: search,
      }}
      className="inline-block me-2 p-4 border-b-2 rounded-t-lg"
      style={isActiveStyle}
      preventScrollReset
      replace
    >
      {children}
    </NavLink>
  );
}

const PRODUCT_VARIANT_FRAGMENT = `#graphql
  fragment ProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      smallUrl: url(transform: {maxWidth: 450, maxHeight: 450, crop: CENTER})
      bigUrl: url(transform: {maxWidth: 450, maxHeight: 450, crop: CENTER, scale: 2})
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
      tags
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
`;

const RELATED_PRODUCT_FRAGMENT = `#graphql
  fragment RelatedProduct on Product {
    id
    title
    handle
    images(first: 1) {
      nodes {
        id
        url
        altText
        width
        height
      }
    }
  }
`;

const PRODUCT_FRAGMENT = `#graphql
  fragment Product on Product {
    id
    title
    vendor
    handle
    collections(first: 5) {
      nodes {
        title
        handle
      }
    }
    descriptionHtml
    description
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...ProductVariant
        }
      }
    }
    images(first: 3) {
      nodes {
        id
        altText
        url
        smallUrl: url(transform: {maxWidth: 450, maxHeight: 450, crop: CENTER})
        bigUrl: url(transform: {maxWidth: 450, maxHeight: 450, crop: CENTER, scale: 2})
        height
        width
      }
    }
    moq: metafield (namespace: "custom", key: "moq") {
      value
    }
    shortDescription: metafield (namespace: "custom", key: "short_description") {
      value
    }
    technicalData: metafield (namespace: "custom", key: "technical_data") {
      value
    }
    downloads: metafield (namespace: "custom", key: "downloads") {
      value
    }
    relatedProducts: metafield (namespace: "shopify--discovery--product_recommendation", key: "related_products") {
      references(first: 10) {
        nodes {
          ... on Product {
            ...RelatedProduct
          }
        }
      }
    }
    selectedVariant: variantBySelectedOptions(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
      ...ProductVariant
    }
    variants(first: 1) {
      nodes {
        ...ProductVariant
      }
    }
    seo {
      description
      title
    }
  }
  ${RELATED_PRODUCT_FRAGMENT}
  ${PRODUCT_VARIANT_FRAGMENT}
`;

const PRODUCT_QUERY = `#graphql
  query Product(
    $country: CountryCode
    $handle: String!
    $language: LanguageCode
    $selectedOptions: [SelectedOptionInput!]!
  ) @inContext(country: $country, language: $language) {
    product(handle: $handle) {
      ...Product
    }
  }
  ${PRODUCT_FRAGMENT}
`;

const PRODUCT_VARIANTS_FRAGMENT = `#graphql
  fragment ProductVariants on Product {
    variants(first: 250) {
      nodes {
        ...ProductVariant
      }
    }
  }
  ${PRODUCT_VARIANT_FRAGMENT}
`;

const VARIANTS_QUERY = `#graphql
  ${PRODUCT_VARIANTS_FRAGMENT}
  query ProductVariants(
    $country: CountryCode
    $language: LanguageCode
    $handle: String!
  ) @inContext(country: $country, language: $language) {
    product(handle: $handle) {
      ...ProductVariants
    }
  }
`;

/** @typedef {import('@shopify/remix-oxygen').LoaderFunctionArgs} LoaderFunctionArgs */
/** @template T @typedef {import('@remix-run/react').MetaFunction<T>} MetaFunction */
/** @typedef {import('storefrontapi.generated').ProductFragment} ProductFragment */
/** @typedef {import('@shopify/hydrogen/storefront-api-types').SelectedOption} SelectedOption */
/** @typedef {import('@shopify/remix-oxygen').SerializeFrom<typeof loader>} LoaderReturnData */
