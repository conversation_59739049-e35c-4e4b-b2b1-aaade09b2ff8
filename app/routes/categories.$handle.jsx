import {defer, json} from '@remix-run/node';
import {Analytics} from '@shopify/hydrogen';
import {useLoaderData} from '@remix-run/react';
import {HEADER_QUERY} from '~/lib/fragments';
import {ImageCard} from '~/components/ImageCard';
import {Breadcrumbs} from '~/components/Breadcrumbs';
import {getMenuUrl, findBreadcrumbsByHandle} from '~/lib/utils';

export const meta = ({data}) => {
  return [
    {title: `${data?.collection.title ?? ''} | Glove Box Australia Pty Ltd`},
  ];
};

export async function loader(args) {
  const criticalData = await loadCriticalData(args);

  return defer({...criticalData});
}

async function loadCriticalData({context, params, request}) {
  const {storefront} = context;
  const {handle} = params;

  const [{menu}, {collection}] = await Promise.all([
    storefront.query(HEADER_QUERY, {
      variables: {
        headerMenuHandle: 'main-menu',
      },
    }),
    storefront.query(SIMPLE_COLLECTION_QUERY, {
      variables: {
        handle,
      },
    }),
  ]);

  function findItems(menuItems, handle) {
    if (!menuItems) return [];

    for (const item of menuItems) {
      if (item.url.includes(handle)) {
        return item.items || [];
      }

      if (item.items) {
        const foundItems = findItems(item.items, handle);
        if (foundItems.length > 0) return foundItems;
      }
    }

    return [];
  }

  const subitems = findItems(menu.items, handle);
  const titles = `title:${subitems
    .map((subcategory) => `"${subcategory.title}"`)
    .join(' OR ')}`;

  const breadcrumbs = findBreadcrumbsByHandle(menu.items, handle);

  return {
    menu,
    subitems,
    collection,
    breadcrumbs,
  };
}

export default function Categories() {
  const {subitems, collection, breadcrumbs} = useLoaderData();

  return (
    <div className="flex flex-col items-center p-4">
      <div className="page-container flex flex-col items-center">
        <Breadcrumbs breadcrumbs={breadcrumbs} />
        <h1 className="font-medium text-xl">{collection?.title}</h1>
        <div
          dangerouslySetInnerHTML={{__html: collection?.descriptionHtml}}
          className="m-2"
        />
        <div className="products-grid">
          {subitems &&
            subitems.map((subcategory) => {
              const handle = subcategory?.resource?.handle;
              const imageData = subcategory?.resource?.image;
              return (
                <ImageCard
                  key={subcategory.id}
                  to={getMenuUrl(subcategory)}
                  title={subcategory.title}
                  imageData={imageData}
                ></ImageCard>
              );
            })}
        </div>
      </div>
      <Analytics.CollectionView
        data={{
          collection: {
            id: collection.id,
            handle: collection.handle,
          },
        }}
      />
    </div>
  );
}

const SIMPLE_COLLECTION_QUERY = `#graphql
  query SimpleCollection(
    $handle: String!
    $country: CountryCode
    $language: LanguageCode
  ) @inContext(country: $country, language: $language) {
    collection(handle: $handle) {
      id
      handle
      title
      descriptionHtml
    }
  }
`;
