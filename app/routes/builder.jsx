// ($slug)._index.tsx
import {
  Content,
  fetchOneEntry,
  getBuilderSearchParams,
  isPreviewing,
} from '@builder.io/sdk-react';
import { useLoaderData, useRouteLoaderData } from '@remix-run/react';
// import { fetch as webFetch } from '@remix-run/web-fetch';

/**
 * @param {LoaderFunctionArgs} args
 */
export const loader = async (args) => {
  const { request, params, context } = args;
  const url = new URL(request.url);
  // const urlPath = `/builder/${params['slug'] || ''}`;
  const urlPath = `/builder`
  const apiKey = context.env.BUILDER_API_KEY;

  const page = await fetchOneEntry({
    model: 'page',
    apiKey: apiKey,
    options: getBuilderSearchParams(url.searchParams),
    userAttributes: { urlPath }
  });

  if (!page && !isPreviewing(url.search)) {
    throw new Response('Page Not Found', {
      status: 404,
      statusText: 'Page not found in Builder.io',
    });
  }

  return { page, apiKey };
};

// Define and render the page.
export default function Page() {
  // Use the useLoaderData hook to get the Page data from `loader` above.
  const { page, apiKey } = useLoaderData();

  // Render the page content from Builder.io
  return <Content model="page" apiKey={apiKey} content={page} />;
}

/** @typedef {import('@shopify/remix-oxygen').LoaderFunctionArgs} LoaderFunctionArgs */