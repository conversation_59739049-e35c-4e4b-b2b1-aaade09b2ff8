import {json} from '@remix-run/node';
import {
  Form,
  NavLink,
  Outlet,
  useLoaderData,
  useRouteLoaderData,
} from '@remix-run/react';
import {CUSTOMER_DETAILS_QUERY} from '~/graphql/customer-account/CustomerDetailsQuery';
import {cn} from '~/lib/utils';

export function shouldRevalidate() {
  return true;
}

/**
 * @param {LoaderFunctionArgs}
 */
export async function loader({context}) {
  const {data, errors} = await context.customerAccount.query(
    CUSTOMER_DETAILS_QUERY,
  );

  if (errors?.length || !data?.customer) {
    throw new Error('Customer not found');
  }

  const customer = {};
  customer.email = data?.customer?.emailAddress?.emailAddress;
  customer.firstName = data?.customer?.firstName;
  customer.lastName = data?.customer?.lastName;
  customer.id = data?.customer?.id;
  customer.tags = data?.customer?.tags;
  customer.defaultAddress = data?.customer?.defaultAddress;
  customer.addresses = data?.customer?.addresses?.nodes;
  // Metafields
  if (data.customer.metafields) {
    for (const metafield of data.customer.metafields) {
      if (!metafield) continue;

      const {key, value} = metafield;
      if (typeof value === 'string' && value.length) {
        customer[key] = value;
      }
    }
  }

  return json(
    {customer},
    {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    },
  );
}

export default function AccountLayout() {
  const {customer} = useRouteLoaderData('root');

  return (
    <div className="account flex justify-center p-8">
      <div className="w-[800px] md:flex">
        <AccountMenu />
        <div className="p-8 rounded-lg w-full flex flex-col bg-white">
          <Outlet context={{customer}} />
        </div>
      </div>
    </div>
  );
}

function AccountMenu() {
  const tabClass =
    'inline-flex items-center px-4 py-3 rounded-lg opacity-70 hover:opacity-100 bg-white w-full';
  function isActiveStyle({isActive, isPending}) {
    return {
      // fontWeight: isActive ? 'bold' : undefined,
      opacity: isActive ? 1 : undefined,
      color: isActive ? 'white' : '#111827',
      backgroundColor: isActive ? '#25aadd' : undefined,
    };
  }

  return (
    <ul className="mt-8 flex-column space-y space-y-4 text-sm font-medium text-gray-500 dark:text-gray-400 md:me-4 mb-4 md:mb-0">
      <li>
        <NavLink
          to="/account/profile"
          style={isActiveStyle}
          className={tabClass}
        >
          Profile
        </NavLink>
      </li>
      <li>
        <NavLink
          to="/account/orders"
          style={isActiveStyle}
          className={tabClass}
        >
          Orders
        </NavLink>
      </li>
      <li>
        <NavLink
          to="/account/addresses"
          style={isActiveStyle}
          className={tabClass}
        >
          Addresses
        </NavLink>
      </li>
      <li>
        <Form
          className={cn(tabClass, 'text-red-500')}
          method="POST"
          action="/account/logout"
        >
          &nbsp;<button type="submit">Logout</button>
        </Form>
      </li>
    </ul>
  );
}

/** @typedef {import('@remix-run/node').LoaderFunctionArgs} LoaderFunctionArgs */
/** @typedef {import('@remix-run/node').SerializeFrom<typeof loader>} LoaderReturnData */
