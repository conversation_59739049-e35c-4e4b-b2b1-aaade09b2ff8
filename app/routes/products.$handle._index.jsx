import {redirect} from '@remix-run/node';
import {json} from '@remix-run/node';

export async function loader({params, request}) {
  const url = new URL(request.url);
  const searchParams = url.searchParams;
  const paramsString =
    searchParams.size > 0 ? `?${searchParams.toString()}` : '';
  return redirect(`/products/${params.handle}/description${paramsString}`);
}

/** @typedef {import('@remix-run/node').SerializeFrom<typeof loader>} LoaderReturnData */
