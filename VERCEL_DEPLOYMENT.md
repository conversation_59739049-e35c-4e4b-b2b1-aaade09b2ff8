# Vercel Deployment Guide

This Shopify Hydrogen project has been configured for deployment on Vercel instead of Shopify's Oxygen platform.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Shopify Store**: You need a Shopify store with API access
3. **Environment Variables**: Configure the required environment variables

## Environment Variables

Copy `.env.example` to `.env` and fill in your Shopify store details:

```bash
cp .env.example .env
```

Required environment variables:

- `PUBLIC_STOREFRONT_API_TOKEN` - Your Shopify Storefront API access token
- `PRIVATE_STOREFRONT_API_TOKEN` - Your private Storefront API access token
- `PUBLIC_STORE_DOMAIN` - Your shop domain (e.g., your-shop.myshopify.com)
- `PUBLIC_STOREFRONT_ID` - Your Storefront ID
- `PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID` - Customer Account API client ID
- `PUBLIC_CUSTOMER_ACCOUNT_API_URL` - Customer Account API URL
- `PUBLIC_CHECKOUT_DOMAIN` - Your checkout domain
- `SESSION_SECRET` - A random secret key for sessions
- `SHOP_ID` - Your Shopify shop ID

## Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Deployment to Vercel

### Option 1: Vercel CLI

1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy:
   ```bash
   vercel
   ```

4. Set environment variables in Vercel dashboard or via CLI:
   ```bash
   vercel env add PUBLIC_STOREFRONT_API_TOKEN
   vercel env add PRIVATE_STOREFRONT_API_TOKEN
   # ... add all other environment variables
   ```

### Option 2: GitHub Integration

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push

## Key Changes Made for Vercel

1. **Package Dependencies**: Added `@remix-run/node` and `@remix-run/serve`
2. **Import Updates**: Replaced `@shopify/remix-oxygen` imports with `@remix-run/node`
3. **Server Configuration**: Updated `server.js` for Vercel's Node.js environment
4. **Build Scripts**: Updated package.json scripts for standard Remix build
5. **Vite Configuration**: Configured with `vercelPreset()`
6. **Context Adaptation**: Modified app context for Node.js instead of Cloudflare Workers

## Important Notes

- This project maintains all Shopify Hydrogen functionality
- GraphQL codegen still works with `npm run codegen`
- All Shopify integrations (Storefront API, Customer Account API) remain functional
- The project structure and components are unchanged

## Troubleshooting

If you encounter issues:

1. Ensure all environment variables are set correctly
2. Check that your Shopify API tokens have the correct permissions
3. Verify your domain configurations match your Shopify settings
4. Review Vercel function logs for runtime errors

## Support

For Vercel-specific issues, refer to [Vercel Documentation](https://vercel.com/docs)
For Shopify Hydrogen issues, refer to [Hydrogen Documentation](https://shopify.dev/docs/custom-storefronts/hydrogen)
