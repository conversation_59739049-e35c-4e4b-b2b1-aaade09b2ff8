{"buildManifest": {"serverBundles": {"nodejs-eyJydW50aW1lIjoibm9kZWpzIn0": {"id": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "file": "build/server/nodejs-eyJydW50aW1lIjoibm9kZWpzIn0/index.js", "config": {"runtime": "nodejs"}}}, "routeIdToServerBundleId": {"routes/blogs.$blogHandle.$articleHandle": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/products.$handle.technical-data": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/products.$handle.description": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/products.$handle.downloads": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/blogs.$blogHandle._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/products.$handle._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account.orders._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/collections.$handle": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account.orders.$id": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account_.authorize": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/categories.$handle": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/collections._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account.addresses": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/policies.$handle": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account.profile": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account_.logout": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/collections.all": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/policies._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account_.login": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/discount.$code": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/[sitemap.xml]": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/pages.$handle": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/[robots.txt]": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/blogs._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/cart.$lines": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/account.$": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/_index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/search": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/cart": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/$": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0"}, "routes": {"root": {"path": "", "id": "root", "file": "app/root.jsx", "config": {}}, "routes/blogs.$blogHandle.$articleHandle": {"file": "app/routes/blogs.$blogHandle.$articleHandle.jsx", "id": "routes/blogs.$blogHandle.$articleHandle", "path": "blogs/:blogHandle/:articleHandle", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/products.$handle.technical-data": {"file": "app/routes/products.$handle.technical-data.jsx", "id": "routes/products.$handle.technical-data", "path": "technical-data", "parentId": "routes/products.$handle", "config": {"runtime": "nodejs"}}, "routes/products.$handle.description": {"file": "app/routes/products.$handle.description.jsx", "id": "routes/products.$handle.description", "path": "description", "parentId": "routes/products.$handle", "config": {"runtime": "nodejs"}}, "routes/products.$handle.downloads": {"file": "app/routes/products.$handle.downloads.jsx", "id": "routes/products.$handle.downloads", "path": "downloads", "parentId": "routes/products.$handle", "config": {"runtime": "nodejs"}}, "routes/blogs.$blogHandle._index": {"file": "app/routes/blogs.$blogHandle._index.jsx", "id": "routes/blogs.$blogHandle._index", "path": "blogs/:blogHandle", "index": true, "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/products.$handle._index": {"file": "app/routes/products.$handle._index.jsx", "id": "routes/products.$handle._index", "index": true, "parentId": "routes/products.$handle", "config": {"runtime": "nodejs"}}, "routes/account.orders._index": {"file": "app/routes/account.orders._index.jsx", "id": "routes/account.orders._index", "path": "orders", "index": true, "parentId": "routes/account", "config": {"runtime": "nodejs"}}, "routes/collections.$handle": {"file": "app/routes/collections.$handle.jsx", "id": "routes/collections.$handle", "path": "collections/:handle", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/account.orders.$id": {"file": "app/routes/account.orders.$id.jsx", "id": "routes/account.orders.$id", "path": "orders/:id", "parentId": "routes/account", "config": {"runtime": "nodejs"}}, "routes/account_.authorize": {"file": "app/routes/account_.authorize.jsx", "id": "routes/account_.authorize", "path": "account/authorize", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/categories.$handle": {"file": "app/routes/categories.$handle.jsx", "id": "routes/categories.$handle", "path": "categories/:handle", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/collections._index": {"file": "app/routes/collections._index.jsx", "id": "routes/collections._index", "path": "collections", "index": true, "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/account.addresses": {"file": "app/routes/account.addresses.jsx", "id": "routes/account.addresses", "path": "addresses", "parentId": "routes/account", "config": {"runtime": "nodejs"}}, "routes/policies.$handle": {"file": "app/routes/policies.$handle.jsx", "id": "routes/policies.$handle", "path": "policies/:handle", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/products.$handle": {"file": "app/routes/products.$handle.jsx", "id": "routes/products.$handle", "path": "products/:handle", "parentId": "root", "config": {}}, "routes/account.profile": {"file": "app/routes/account.profile.jsx", "id": "routes/account.profile", "path": "profile", "parentId": "routes/account", "config": {"runtime": "nodejs"}}, "routes/account_.logout": {"file": "app/routes/account_.logout.jsx", "id": "routes/account_.logout", "path": "account/logout", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/collections.all": {"file": "app/routes/collections.all.jsx", "id": "routes/collections.all", "path": "collections/all", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/policies._index": {"file": "app/routes/policies._index.jsx", "id": "routes/policies._index", "path": "policies", "index": true, "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/account._index": {"file": "app/routes/account._index.jsx", "id": "routes/account._index", "index": true, "parentId": "routes/account", "config": {"runtime": "nodejs"}}, "routes/account_.login": {"file": "app/routes/account_.login.jsx", "id": "routes/account_.login", "path": "account/login", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/discount.$code": {"file": "app/routes/discount.$code.jsx", "id": "routes/discount.$code", "path": "discount/:code", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/[sitemap.xml]": {"file": "app/routes/[sitemap.xml].jsx", "id": "routes/[sitemap.xml]", "path": "sitemap.xml", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/pages.$handle": {"file": "app/routes/pages.$handle.jsx", "id": "routes/pages.$handle", "path": "pages/:handle", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/[robots.txt]": {"file": "app/routes/[robots.txt].jsx", "id": "routes/[robots.txt]", "path": "robots.txt", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/blogs._index": {"file": "app/routes/blogs._index.jsx", "id": "routes/blogs._index", "path": "blogs", "index": true, "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/cart.$lines": {"file": "app/routes/cart.$lines.jsx", "id": "routes/cart.$lines", "path": ":lines", "parentId": "routes/cart", "config": {"runtime": "nodejs"}}, "routes/account.$": {"file": "app/routes/account.$.jsx", "id": "routes/account.$", "path": "*", "parentId": "routes/account", "config": {"runtime": "nodejs"}}, "routes/account": {"file": "app/routes/account.jsx", "id": "routes/account", "path": "account", "parentId": "root", "config": {}}, "routes/_index": {"file": "app/routes/_index.jsx", "id": "routes/_index", "index": true, "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/search": {"file": "app/routes/search.jsx", "id": "routes/search", "path": "search", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/cart": {"file": "app/routes/cart.jsx", "id": "routes/cart", "path": "cart", "parentId": "root", "config": {"runtime": "nodejs"}}, "routes/$": {"file": "app/routes/$.jsx", "id": "routes/$", "path": "*", "parentId": "root", "config": {"runtime": "nodejs"}}}}, "remixConfig": {"appDirectory": "/home/<USER>/projects/labhub/labhub-webshop/app", "basename": "/", "buildDirectory": "/home/<USER>/projects/labhub/labhub-webshop/build", "future": {"v3_fetcherPersist": true, "v3_relativeSplatPath": true, "v3_throwAbortReason": true, "v3_routeConfig": false, "v3_singleFetch": false, "v3_lazyRouteDiscovery": false, "unstable_optimizeDeps": false}, "manifest": false, "publicPath": "/", "routes": {"root": {"path": "", "id": "root", "file": "root.jsx"}, "routes/blogs.$blogHandle.$articleHandle": {"file": "routes/blogs.$blogHandle.$articleHandle.jsx", "id": "routes/blogs.$blogHandle.$articleHandle", "path": "blogs/:blogHandle/:articleHandle", "parentId": "root"}, "routes/products.$handle.technical-data": {"file": "routes/products.$handle.technical-data.jsx", "id": "routes/products.$handle.technical-data", "path": "technical-data", "parentId": "routes/products.$handle"}, "routes/products.$handle.description": {"file": "routes/products.$handle.description.jsx", "id": "routes/products.$handle.description", "path": "description", "parentId": "routes/products.$handle"}, "routes/products.$handle.downloads": {"file": "routes/products.$handle.downloads.jsx", "id": "routes/products.$handle.downloads", "path": "downloads", "parentId": "routes/products.$handle"}, "routes/blogs.$blogHandle._index": {"file": "routes/blogs.$blogHandle._index.jsx", "id": "routes/blogs.$blogHandle._index", "path": "blogs/:blogHandle", "index": true, "parentId": "root"}, "routes/products.$handle._index": {"file": "routes/products.$handle._index.jsx", "id": "routes/products.$handle._index", "index": true, "parentId": "routes/products.$handle"}, "routes/account.orders._index": {"file": "routes/account.orders._index.jsx", "id": "routes/account.orders._index", "path": "orders", "index": true, "parentId": "routes/account"}, "routes/collections.$handle": {"file": "routes/collections.$handle.jsx", "id": "routes/collections.$handle", "path": "collections/:handle", "parentId": "root"}, "routes/account.orders.$id": {"file": "routes/account.orders.$id.jsx", "id": "routes/account.orders.$id", "path": "orders/:id", "parentId": "routes/account"}, "routes/account_.authorize": {"file": "routes/account_.authorize.jsx", "id": "routes/account_.authorize", "path": "account/authorize", "parentId": "root"}, "routes/categories.$handle": {"file": "routes/categories.$handle.jsx", "id": "routes/categories.$handle", "path": "categories/:handle", "parentId": "root"}, "routes/collections._index": {"file": "routes/collections._index.jsx", "id": "routes/collections._index", "path": "collections", "index": true, "parentId": "root"}, "routes/account.addresses": {"file": "routes/account.addresses.jsx", "id": "routes/account.addresses", "path": "addresses", "parentId": "routes/account"}, "routes/policies.$handle": {"file": "routes/policies.$handle.jsx", "id": "routes/policies.$handle", "path": "policies/:handle", "parentId": "root"}, "routes/products.$handle": {"file": "routes/products.$handle.jsx", "id": "routes/products.$handle", "path": "products/:handle", "parentId": "root"}, "routes/account.profile": {"file": "routes/account.profile.jsx", "id": "routes/account.profile", "path": "profile", "parentId": "routes/account"}, "routes/account_.logout": {"file": "routes/account_.logout.jsx", "id": "routes/account_.logout", "path": "account/logout", "parentId": "root"}, "routes/collections.all": {"file": "routes/collections.all.jsx", "id": "routes/collections.all", "path": "collections/all", "parentId": "root"}, "routes/policies._index": {"file": "routes/policies._index.jsx", "id": "routes/policies._index", "path": "policies", "index": true, "parentId": "root"}, "routes/account._index": {"file": "routes/account._index.jsx", "id": "routes/account._index", "index": true, "parentId": "routes/account"}, "routes/account_.login": {"file": "routes/account_.login.jsx", "id": "routes/account_.login", "path": "account/login", "parentId": "root"}, "routes/discount.$code": {"file": "routes/discount.$code.jsx", "id": "routes/discount.$code", "path": "discount/:code", "parentId": "root"}, "routes/[sitemap.xml]": {"file": "routes/[sitemap.xml].jsx", "id": "routes/[sitemap.xml]", "path": "sitemap.xml", "parentId": "root"}, "routes/pages.$handle": {"file": "routes/pages.$handle.jsx", "id": "routes/pages.$handle", "path": "pages/:handle", "parentId": "root"}, "routes/[robots.txt]": {"file": "routes/[robots.txt].jsx", "id": "routes/[robots.txt]", "path": "robots.txt", "parentId": "root"}, "routes/blogs._index": {"file": "routes/blogs._index.jsx", "id": "routes/blogs._index", "path": "blogs", "index": true, "parentId": "root"}, "routes/cart.$lines": {"file": "routes/cart.$lines.jsx", "id": "routes/cart.$lines", "path": ":lines", "parentId": "routes/cart"}, "routes/account.$": {"file": "routes/account.$.jsx", "id": "routes/account.$", "path": "*", "parentId": "routes/account"}, "routes/account": {"file": "routes/account.jsx", "id": "routes/account", "path": "account", "parentId": "root"}, "routes/_index": {"file": "routes/_index.jsx", "id": "routes/_index", "index": true, "parentId": "root"}, "routes/search": {"file": "routes/search.jsx", "id": "routes/search", "path": "search", "parentId": "root"}, "routes/cart": {"file": "routes/cart.jsx", "id": "routes/cart", "path": "cart", "parentId": "root"}, "routes/$": {"file": "routes/$.jsx", "id": "routes/$", "path": "*", "parentId": "root"}}, "serverBuildFile": "index.js", "serverModuleFormat": "esm", "ssr": true}, "viteConfig": {"build": {"assetsDir": "assets"}}}