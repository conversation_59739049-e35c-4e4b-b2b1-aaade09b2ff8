# Shopify Storefront API Configuration
PUBLIC_STOREFRONT_API_TOKEN=your_storefront_access_token
PRIVATE_STOREFRONT_API_TOKEN=your_private_storefront_access_token
PUBLIC_STORE_DOMAIN=your-shop.myshopify.com
PUBLIC_STOREFRONT_ID=your_storefront_id

# Customer Account API Configuration
PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID=your_customer_account_api_client_id
PUBLIC_CUSTOMER_ACCOUNT_API_URL=https://shopify.com/your_shop_id/account/customer/api/unstable/graphql

# Checkout Configuration
PUBLIC_CHECKOUT_DOMAIN=your-shop.myshopify.com

# Session Configuration
SESSION_SECRET=your_session_secret_key

# Shop Configuration
SHOP_ID=your_shop_id
